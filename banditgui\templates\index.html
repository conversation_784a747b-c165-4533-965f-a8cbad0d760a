<!-- Skip navigation link for accessibility -->
<a href="#main-content" class="skip-link">Skip to main content</a>
<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>BanditGUI - OverTheWire Bandit CTF</title>
        <link rel="preconnect" href="https://fonts.googleapis.com">
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
        <link rel="stylesheet" href="{{ url_for('static', filename='dist/styles.css') }}">
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
        <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js" integrity="sha512-rCQgmUulW6f6QegOvTntKKb5IAoxTpGVCdWqYjkXEpzAns6XUFs8NKVqWe+KQpctp/EoRSFSuykVputqknLYMg==" crossorigin="anonymous"></script>
        <style>
          .visually-hidden { position: absolute; width: 1px; height: 1px; padding: 0; margin: -1px; overflow: hidden; clip: rect(0,0,0,0); border: 0; }
        </style>
    </head>
    <body data-ollama-api-url="{{ OLLAMA_API_URL|default('') }}">
        <header role="banner">
            <div class="visually-hidden">
                <h1>BanditGUI - OverTheWire Bandit CTF</h1>
            </div>
        </header>
        <main role="main" id="main-content">
        <div class="container" role="group" aria-label="Main content panels">
            <!-- Left Panel (Chat/Help) -->
            <div class="panel left" role="region" aria-labelledby="assistant-title">
                <div id="chat-container" role="log">
                    <div id="chat-messages" aria-live="polite" aria-relevant="additions" aria-label="Chat messages">
                        <div class="system-message" role="status">
                            <h3>🎯 Welcome to BanditGUI!</h3>
                            <p>Your interactive gateway to the <strong>OverTheWire Bandit</strong> cybersecurity challenges.</p>
                            <p>🚀 Ready to start your hacking journey? Click the button below to begin Level 0!</p>
                            <div class="start-game-container">
                                <button id="start-game-button" class="start-game-button" aria-label="Start a new game">🎮 Start Level 0</button>
                            </div>
                            <details style="margin-top: 15px;">
                                <summary style="cursor: pointer; color: #00ff00; font-weight: bold;">📋 Available Commands</summary>
                                <nav aria-label="Chat commands" style="margin-top: 10px;">
                                    <ul role="list" style="margin-left: 20px;">
                                    <li><code>start</code> - Display Level 0 instructions</li>
                                    <li><code>help</code> - Get general information about Bandit wargame</li>
                                    <li><code>info</code> - Check connection status and current level</li>
                                    <li><code>level</code> - Get instructions for the current level</li>
                                    <li><code>hint</code> - Get hints for the current level</li>
                                    <li><code>quit</code> - Disconnect and exit the game</li>
                                    </ul>
                                </nav>
                                <p style="margin-top: 10px;"><em>💡 Use the terminal on the right to execute commands on the Bandit server. Ask me for help anytime!</em></p>
                            </details>
                        </div>
                    </div>
                </div>
                <div id="chat-input-container">
                    <label for="chat-input" class="visually-hidden">Chat input</label>
                    <input type="text" id="chat-input" placeholder="Chat with the assistant..." aria-label="Chat input">
                    <button id="chat-submit" aria-label="Send chat message"><i class="fas fa-paper-plane"></i></button>
                </div>
            </div>

            <!-- Right Panel (Terminal) -->
            <div class="panel right" role="complementary" aria-label="Terminal Panel">
                <div class="terminal-header">
                    <div class="header-right-controls">
                        <div id="ask-a-pro-container" class="ask-a-pro-controls">
                            <button id="ask-a-pro-button" class="chat-button ask-a-pro-button" aria-label="Ask a pro">
                                <i class="fas fa-user-graduate"></i> Ask-a-Pro
                            </button>
                        </div>
                        <!-- Only a visually-hidden label is used for accessibility; do not add aria-label to avoid redundancy. -->
                        <label for="llm-selection-dropdown" class="visually-hidden">Language Model Selection</label>
                        <select id="llm-selection-dropdown" class="llm-dropdown">
                            <!-- Options will be populated by JavaScript -->
                        </select>
                        <div class="connection-status" role="status" aria-live="polite">
                            <span id="status-indicator" class="disconnected" aria-hidden="true"></span>
                            <span id="status-text" aria-label="Connection status">Disconnected</span>
                        </div>
                    </div>
                </div>
                <div id="terminal-container"
                     role="application"
                     aria-label="Bandit Terminal"
                     tabindex="0"
                     aria-roledescription="Interactive SSH terminal for Bandit challenges"></div>
                <!-- Terminal footer removed -->
            </div>
        </div>
        </main>
        
        <footer role="contentinfo">
            <div class="visually-hidden">
                <p>BanditGUI - A learning tool for OverTheWire Bandit CTF challenges</p>
            </div>
        </footer>

        <!-- xterm.js and addons -->
        <link rel="stylesheet" href="{{ url_for('static', filename='xterm.css') }}">
        <link rel="stylesheet" href="{{ url_for('static', filename='xterm-custom.css') }}">
        <script src="{{ url_for('static', filename='js/xterm.js') }}"></script>
        <script src="{{ url_for('static', filename='js/xterm-addon-fit.js') }}"></script>
        <script src="{{ url_for('static', filename='js/xterm-addon-web-links.js') }}"></script>

        <!-- Your main bundle -->
        <script src="{{ url_for('static', filename='dist/main.js') }}"></script>
    </body>
</html>
